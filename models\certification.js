import mongoose from 'mongoose';

// Certification Schema (standalone)
const CertificationSchema = new mongoose.Schema({
  freelancerId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Freelancer',
    required: true 
  },
  name: { type: String, required: true },
  issuingOrganization: { type: String, required: true },
  credentialId: String,
  credentialUrl: String,
  issueDate: { type: Date, required: true },
  expirationDate: Date,
  skillsVerified: [String],
  verification: {
    isVerified: { type: Boolean, default: false },
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    verifiedAt: Date
  }
}, { timestamps: true });

export default mongoose.model('Certification', CertificationSchema);
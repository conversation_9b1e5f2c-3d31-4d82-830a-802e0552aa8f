import Freelancer from '../models/freelancer.js';

// Create Freelancer
export const createFreelancer = async (req, res) => {
  try {
    const freelancer = new Freelancer(req.body);
    await freelancer.save();
    res.status(201).json(freelancer);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get All Freelancers
export const getFreelancers = async (req, res) => {
  try {
    const freelancers = await Freelancer.find().populate('userId');
    res.json(freelancers);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Freelancer By ID
export const getFreelancerById = async (req, res) => {
  try {
    const freelancer = await Freelancer.findById(req.params.id).populate('userId');
    if (!freelancer) return res.status(404).json({ message: 'Freelancer not found' });
    res.json(freelancer);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update Freelancer
export const updateFreelancer = async (req, res) => {
  try {
    const freelancer = await Freelancer.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!freelancer) return res.status(404).json({ message: 'Freelancer not found' });
    res.json(freelancer);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete Freelancer
export const deleteFreelancer = async (req, res) => {
  try {
    const freelancer = await Freelancer.findByIdAndDelete(req.params.id);
    if (!freelancer) return res.status(404).json({ message: 'Freelancer not found' });
    res.json({ message: 'Freelancer deleted' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

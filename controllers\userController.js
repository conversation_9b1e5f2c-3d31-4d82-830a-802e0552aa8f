import User from '../models/user.js';
import Client from '../models/client.js';
import Freelancer from '../models/freelancer.js';

// Create User
export const createUser = async (req, res) => {
  try {
    const { fullName, email, password, userType, phone } = req.body;

    // Split the fullName into firstName and lastName
    const [firstName, lastName] = fullName.split(' ');

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'Email already exists. Please choose another one.' });
    }

    if (!firstName && !lastName) {
      return res.status(400).json({ message: 'Full name must include both first and last name' });
    }

    // Create the new user with the split firstName and lastName
    const user = new User({
      email,
      password,
      firstName,
      lastName,
      role:userType,
      phone
    });


    if(userType==='client'){
        const client = new Client({userId: user._id});
        await client.save();
    }
        if(userType==='freelancer'){
        const freelancer = new Freelancer({userId: user._id});
        await freelancer.save();
    }

        // Save user to database
    await user.save();

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get All Users
export const getUsers = async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get User By ID
export const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) return res.status(404).json({ message: 'User not found' });
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update User
export const updateUser = async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!user) return res.status(404).json({ message: 'User not found' });
    res.json(user);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete User
export const deleteUser = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);
    if (!user) return res.status(404).json({ message: 'User not found' });
    res.json({ message: 'User deleted' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

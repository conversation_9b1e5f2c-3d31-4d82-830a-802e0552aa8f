import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../models/user.js';

// Login Controller
export const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;
    // Check if the user exists
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(400).json({ message: 'User not found' });
    }
    
    // Compare password with hashed password in DB
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    if (user.status === 'Pending' && user.role !== 'admin') {
      return res.status(400).json({ message: 'Please wait system admin approval' });
    }  else  if (user.status === 'Rejected' && user.role !== 'admin') {
      return res.status(400).json({ message: 'Your request has been rejected!' });
    }

    // Create JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email, role:user.role },
      process.env.JWT_SECRET, // Secret key from env variables
      { expiresIn: '1h' }
    );

    res.status(200).json({ message: 'Login successful',user, token});
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Logout Controller
export const logoutUser = (req, res) => {
  try {
    // Clear the token
    res.clearCookie('token');

    res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Approve User Controller (Admin only)
export const approveUser = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find the user by ID
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already approved
    if (user.status === 'Approved') {
      return res.status(400).json({ message: 'User is already approved' });
    }

    // Update user status to Approved
    user.status = 'Approved';
    user.isActive = true; // Also activate the user
    await user.save();

    res.status(200).json({
      message: 'User approved successfully',
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Error approving user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reject User Controller (Admin only)
export const rejectUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body; // Optional rejection reason

    // Find the user by ID
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already rejected
    if (user.status === 'Rejected') {
      return res.status(400).json({ message: 'User is already rejected' });
    }

    // Update user status to Rejected
    user.status = 'Rejected';
    user.isActive = false; // Deactivate the user

    // Store rejection reason if provided
    if (reason) {
      user.rejectionReason = reason;
    }

    await user.save();

    res.status(200).json({
      message: 'User rejected successfully',
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isActive: user.isActive,
        rejectionReason: reason || null
      }
    });
  } catch (error) {
    console.error('Error rejecting user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get Pending Users Controller (Admin only)
export const getPendingUsers = async (req, res) => {
  try {
    const pendingUsers = await User.find({
      status: 'Pending',
      role: { $ne: 'admin' } // Exclude admin users
    }).select('-password'); // Exclude password field

    res.status(200).json({
      message: 'Pending users retrieved successfully',
      count: pendingUsers.length,
      users: pendingUsers
    });
  } catch (error) {
    console.error('Error fetching pending users:', error);
    res.status(500).json({ message: 'Server error' });
  }
};


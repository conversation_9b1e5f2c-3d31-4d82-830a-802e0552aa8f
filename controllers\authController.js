import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../models/user.js';

// Login Controller
export const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;
    // Check if the user exists
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(400).json({ message: 'User not found' });
    }
    
    // Compare password with hashed password in DB
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    if (user.status === 'Pending' && user.role !== 'admin') {
      return res.status(400).json({ message: 'Please wait system admin approval' });
    }  else  if (user.status === 'Rejected' && user.role !== 'admin') {
      return res.status(400).json({ message: 'Your request has been rejected!' });
    }

    // Create JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email, role:user.role },
      process.env.JWT_SECRET, // Secret key from env variables
      { expiresIn: '1h' }
    );

    res.status(200).json({ message: 'Login successful',user, token});
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Logout Controller
export const logoutUser = (req, res) => {
  try {
    // Clear the token
    res.clearCookie('token'); 
    
    res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
};


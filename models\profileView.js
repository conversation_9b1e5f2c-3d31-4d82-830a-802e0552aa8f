import mongoose from 'mongoose';


// View Tracking Schema
const ProfileViewSchema = new mongoose.Schema({
  freelancerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Freelancer', required: true},
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client', required: true},
  lastViewedAt: { type: Date, default: Date.now },
  views: {  type: Number, default: 0 },
  ratings: {
      average: { type: Number, default: 0 },
      count: { type: Number, default: 0 },
      breakdown: {
        '1': { type: Number, default: 0 },
        '2': { type: Number, default: 0 },
        '3': { type: Number, default: 0 },
        '4': { type: Number, default: 0 },
        '5': { type: Number, default: 0 }
      }
    }
});

export default mongoose.model('ProfileView', ProfileViewSchema); 
import mongoose from 'mongoose';

// Recommendation Schema
const RecommendationSchema = new mongoose.Schema({
  freelancerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Freelancer', required: true},
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client',required: true},
  projectId: { type: mongoose.Schema.Types.ObjectId,ref: 'Project'},
  rating: { type: Number, min: 1, max: 5,required: true},
  comment: { type: String,maxLength: 500},
  skillsEndorsed: [String],
  visibility: {type: String,enum: ['public', 'private'],default: 'public'}
}, { timestamps: true }); // this will add createdAt and updatedAt...

export default mongoose.model('Recommendation', RecommendationSchema);
import mongoose from 'mongoose';

// Privacy Settings Schema
const PrivacySettingSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    unique: true 
  },
  profileVisibility: { 
    type: String, 
    enum: ['public', 'private'],
    default: 'public'
  },
  contactInfoVisibility: {
    email: { type: String, enum: ['public', 'private'], default: 'public' },
    phone: { type: String, enum: ['public', 'private'], default: 'public' }
  },
  activityVisibility: {
    projects: { type: String, enum: ['public', 'private'], default: 'public' },
    certifications: { type: String, enum: ['public', 'private'], default: 'public' }
  },
}, { timestamps: true });

export default mongoose.model('PrivacySetting', PrivacySettingSchema);
import mongoose from 'mongoose';

// Project Model
const ProjectSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client', required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  budget: Number,
  deadline: Date,
  requiredSkills: [String],
  status: { 
    type: String, 
    enum: ['draft', 'open', 'in-progress', 'completed', 'cancelled'],
    default: 'draft'
  },
  applicants: [{
    freelancerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Freelancer' },
    proposal: String,
    bidAmount: Number,
    status: { type: String, enum: ['pending', 'accepted', 'rejected'], default: 'pending' }
  }]
}, { timestamps: true });

export default mongoose.model('Project', ProjectSchema);
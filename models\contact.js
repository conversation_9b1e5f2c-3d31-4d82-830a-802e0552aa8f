import mongoose from 'mongoose';

const ContactSchema = new mongoose.Schema({
  sender: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  receiver: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true},
  senderRole: { type: String,enum: ['client', 'freelancer', 'admin'], required: true  },
  message: { type: String, required: true,maxLength: 2000},
  sentAt: { type: Date, default: Date.now },
  readAt: { type: Date },
  project: { type: mongoose.Schema.Types.ObjectId, ref: 'Project' },
  status: { type: String, enum: ['sent', 'delivered', 'read'], default: 'sent'}
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});


// Virtual for whether message is read
ContactSchema.virtual('isRead').get(function() {
  return !!this.readAt;
});

// Middleware to update status when readAt changes
ContactSchema.pre('save', function(next) {
  if (this.isModified('readAt') && this.readAt) {
    this.status = 'read';
  }
  next();
});

export default mongoose.model('Contact', ContactSchema);
import mongoose from 'mongoose';

const PortfolioSchema = new mongoose.Schema({
  freelancerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Freelancer', required: true},
  title: { type: String, required: true },
  description: { type: String },
  techStack: [String],
  link: { type: String }, // e.g., GitHub, live demo
  completionDate: { type: Date },
},
  {
    timestamps: true, // adds createdAt and updatedAt fields
  }
);

export default  mongoose.model('Portfolio', PortfolioSchema);
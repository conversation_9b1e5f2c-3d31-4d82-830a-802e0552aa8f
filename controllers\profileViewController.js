import ProfileView from '../models/profileView.js';

// Create Profile View
export const createProfileView = async (req, res) => {
  try {
    const profileView = new ProfileView(req.body);
    await profileView.save();
    res.status(201).json(profileView);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get All Profile Views
export const getProfileViews = async (req, res) => {
  try {
    const views = await ProfileView.find().populate('freelancerId viewerId');
    res.json(views);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Profile View By ID
export const getProfileViewById = async (req, res) => {
  try {
    const view = await ProfileView.findById(req.params.id).populate('freelancerId viewerId');
    if (!view) return res.status(404).json({ message: 'Profile view not found' });
    res.json(view);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Delete Profile View
export const deleteProfileView = async (req, res) => {
  try {
    const view = await ProfileView.findByIdAndDelete(req.params.id);
    if (!view) return res.status(404).json({ message: 'Profile view not found' });
    res.json({ message: 'Profile view deleted' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

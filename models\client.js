import mongoose from 'mongoose';

const ClientSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  clientType: { type: String, enum: ['company', 'individual'], default: 'individual' },
  companyName: { type: String },
  companyWebsite: { type: String },
  projectsPosted: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Project' }],

}, { timestamps: true });

export default mongoose.model('Client', ClientSchema);
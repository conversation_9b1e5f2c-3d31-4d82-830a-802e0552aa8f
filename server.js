import express from 'express';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import cors from 'cors';

// Import routes
import userRoutes from './routes/userRoutes.js';
import authRoutes from './routes/authRoutes.js';
import freelancerRoutes from './routes/freelancerRoutes.js';
import clientRoutes from './routes/clientRoutes.js';
import projectRoutes from './routes/projectRoutes.js';
import portfolioRoutes from './routes/portfolioRoutes.js';
import profileViewRoutes from './routes/profileViewRoutes.js';
import { createDefaultAdmin } from './setup/init.js';

dotenv.config(); // Load environment variables from .env

const app = express();

// Configure allowed origins for CORS
const allowedOrigins = [
    'http://localhost:3000',
    'http://127.0.0.1:5500',
    'http://localhost:5500',
    'http://127.0.0.1:3000'
];

// Middleware
app.use(cors({
    origin: function (origin, callback) {
        console.log('CORS Origin received:', origin);

        // Allow requests with no origin (like mobile apps, curl requests, or file:// protocol)
        if (!origin || origin === 'null') {
            console.log('No origin or null origin - allowing request (likely file:// protocol)');
            return callback(null, true);
        }

        // Check if the origin is in our allowed list or starts with file://
        if (allowedOrigins.includes(origin) ||
            origin.startsWith('file://') ||
            origin.startsWith('http://localhost') ||
            origin.startsWith('http://127.0.0.1')) {
            console.log('Origin allowed:', origin);
            return callback(null, true);
        }

        // Reject the request
        console.log('Origin rejected:', origin);
        const msg = `The CORS policy for this site does not allow access from the specified Origin: ${origin}`;
        return callback(new Error(msg), false);
    },
    credentials: true
}));
app.use(express.json());
// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/freelancers', freelancerRoutes);
app.use('/api/clients', clientRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/portfolios', portfolioRoutes);
app.use('/api/profile-views', profileViewRoutes);

// Basic route
app.get('/', (req, res) => {
  res.send('API is running...');
});

app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    uptime: process.uptime(),
    timestamp: new Date(),
    message: 'Server is healthy 🚀'
  });
});

// Connect to MongoDB and start server
const PORT = process.env.PORT || 5000;
const MONGO_URI = process.env.MONGO_URI;

mongoose
  .connect(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  })
  .then(() => {
    console.log('✅ MongoDB connected');
    app.listen(PORT, () => {
      console.log(`🚀 Server running on http://localhost:${PORT}`);
    });
  })
  .catch((error) => {
    console.error('❌ MongoDB connection error:', error.message);
    process.exit(1);
  });


  createDefaultAdmin();

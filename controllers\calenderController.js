import Calender from '../models/calender.js';

// Create Calender
export const createCalender = async (req, res) => {
  try {
    const calender = new Calender(req.body);
    await calender.save();
    res.status(201).json(calender);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get All Calenders
export const getCalenders = async (req, res) => {
  try {
    const calenders = await Calender.find().populate('userId');
    res.json(calenders);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Calender By ID
export const getCalenderById = async (req, res) => {
  try {
    const calender = await Calender.findById(req.params.id).populate('userId');
    if (!calender) return res.status(404).json({ message: 'Calender not found' });
    res.json(calender);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update Calender
export const updateCalender = async (req, res) => {
  try {
    const calender = await Calender.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!calender) return res.status(404).json({ message: 'Calender not found' });
    res.json(calender);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete Calender
export const deleteCalender = async (req, res) => {
  try {
    const calender = await Calender.findByIdAndDelete(req.params.id);
    if (!calender) return res.status(404).json({ message: 'Calender not found' });
    res.json({ message: 'Calender deleted' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

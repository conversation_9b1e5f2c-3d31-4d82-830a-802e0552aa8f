import mongoose from 'mongoose';
import bcrypt from 'bcrypt';

// Base User Model
const UserSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true, select: false },
  firstName: { type: String, required: true },
  lastName: { type: String },
  role: { type: String, enum: ['client', 'freelancer', 'admin'], required: true },
  isActive: { type: Boolean, default: false },
  phone: { type: String },
  status: {type: String,enum: ["Pending", "Approved", "Rejected"],default: "Pending"},

}, { timestamps: true });

// Hash password before saving
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next(); // only hash if password is new or changed
  try {
    // const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, 10);
    return next();
  } catch (err) {
    return next(err);
  }
});

// Optional: method to compare password during login
UserSchema.methods.comparePassword = async function (candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.model('User', UserSchema);

import mongoose from 'mongoose';
import * as Models from '../models/index.js';
import { logger } from '../utils/logging/logger.js';
import { errorHandler } from '../middleware/errorMiddleware.js';
import { permissionChecker } from '../middleware/permissionMiddleware.js';


const handler = (modelName, action) => {
  return async (req, res, next) => {
    try {
      const Model = Models[modelName];
      if (!Model) {
        return next(errorHandler({ statusCode: 404, message: `${modelName} model not found` }));
      }

      logger.info(`Executing ${action} on ${modelName}`, {
        userId: req.userId,
        method: req.method,
        path: req.originalUrl,
        ipAddress: req.ip,
        params: req.params,
        query: req.query
      });

      // const { page = 1, limit = 20, sort = '-createdAt', populate = '' } = req.query;
      // const skip = (Math.max(Number(page), 1) - 1) * Number(limit);
      // const query = req.dataAccessQuery || {}; 

            const { 
        page = 1, 
        limit = 20, 
        sort = '-createdAt', 
        populate = '',
        select = ''
      } = req.query;
      const skip = (Math.max(Number(page), 1) - 1) * Number(limit);

      let query = Model.find(req.dataAccessQuery || {});

      // 1. Apply field selection to MAIN document
      if (select) {
        query = query.select(select.replace(/,/g, ' '));
      }

      // 2. Handle dynamic population with field selection
      if (populate) {
        populate.split('|').forEach(instruction => {
          const [path, fields] = instruction.split(':');
          let select = {};
          
          if (fields) {
            select = fields.replace(/,/g, ' ');
          }
          
          query = query.populate(path, select);
        });
      }

      switch (action) {
        case 'list': {
          const [items, total] = await Promise.all([
            query.skip(skip).limit(Number(limit)).sort(sort),
            Model.countDocuments(req.dataAccessQuery || {})
          ]);
          
          res.status(200).json({
            message: `${modelName} list retrieved successfully`,
            success: true,
            statusCode: 200,
            data: {items,total,page,limit},
            // meta: {
            //   total,
            //   page: page,
            //   limit: limit
            // }
          });
          break;
        }
        
        case 'get': {
          if (!mongoose.isValidObjectId(req.params.id)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
          }
          const item = await query.findById(req.params.id);
          if (!item) {
            return next(errorHandler({ statusCode: 404, message: `${modelName} not found` }));
          }
          res.status(200).json({
            message: `${modelName} retrieved successfully`,
            data: item,
            success: true,
            statusCode: 200,
          });
          break;
        }
        // case 'list': {
        //   const excludedQueryParams = ['page', 'limit', 'sort', 'populate'];
        //   Object.keys(req.query).forEach(key => {
        //     if (!excludedQueryParams.includes(key)) {
        //       query[key] = req.query[key];
        //     }
        //   });

        //   const [items, total] = await Promise.all([
        //     Model.find(query).skip(skip).limit(Number(limit)).sort(sort).populate(populate),
        //     Model.countDocuments(query)
        //   ]);
        //   res.status(200).json({
        //     message: `${modelName} list retrieved successfully`,
        //     success: true,
        //     statusCode: 200,
        //     data: items,
        //     meta: {
        //       total,
        //       page: Number(page),
        //       limit: Number(limit)
        //     }
        //   });
        //   break;
        // }

        // case 'get': {
        //   if (!mongoose.isValidObjectId(req.params.id)) {
        //     return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
        //   }
        //   const item = await Model.findById(req.params.id).populate(populate);
        //   if (!item) {
        //     return next(errorHandler({ statusCode: 404, message: `${modelName} not found` }));
        //   }
        //   return res.json({
        //     message: `${modelName} retrieved successfully`,
        //     data: item
        //   });
        // }

        case 'create': {
          console.log({newItem:req.body})

          const newItem = await Model.create({
            ...req.body,
            assignedBy: req.userId,
            createdBy: req.userId
          });

          logger.info(`${modelName} created`, { id: newItem._id, createdBy: req.userId });
          return res.status(201).json({
            message: `${modelName} created successfully`,
            data: newItem,
            success: true,
            statusCode:201
          });
        }

        case 'update': {
          if (!mongoose.isValidObjectId(req.params.id)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
          }
          const updatePayload = { ...req.body, updatedBy: req.userId, approvedBy:req.userId };
          const updatedItem = await Model.findByIdAndUpdate(
            req.params.id,
            updatePayload,
            { new: true, runValidators: true }
          );

          if (!updatedItem) {
            return next(errorHandler({ statusCode: 404, message: `${modelName} not found` }));
          }

          const updatedFields = Object.keys(req.body);

          logger.info(`${modelName} updated`, {
            id: updatedItem._id,
            updatedBy: req.userId,
            updatedFields
          });

          return res.json({
            message: `${modelName} updated successfully`,
            data: updatedItem,
            success: true,
            statusCode:200
          });
        }

        case 'delete': {
          if (!mongoose.isValidObjectId(req.params.id)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
          }
          const deletedItem = await Model.findByIdAndDelete(req.params.id);
          if (!deletedItem) {
            return next(errorHandler({ statusCode: 404, message: `${modelName} not found` }));
          }
          logger.warn(`${modelName} deleted`, { id: deletedItem._id, deletedBy: req.userId }); // Log deletedBy
          return res.json({
            message: `${modelName} deleted successfully`,
            data: deletedItem,
            success: true,
            statusCode:200
          });
        }

        case 'bulkCreate': {
          const items = req.body.map(item => ({
            ...item,
            createdBy: req.userId
          }));

          const createdItems = await Model.insertMany(items, { ordered: false });
          logger.info(`${createdItems.length} ${modelName} created`, { createdBy: req.userId });
          return res.status(201).json({
            message: `${createdItems.length} ${modelName} created successfully`,
            data: createdItems,
            success: true,
            statusCode:200
          });
        }

        case 'bulkUpdate': {
          const { ids, updates } = req.body;
          if (!Array.isArray(ids) || typeof updates !== 'object') {
            return next(errorHandler({ statusCode: 400, message: 'Invalid bulk update payload' }));
          }
          const result = await Model.updateMany(
            { _id: { $in: ids } },
            { ...updates, updatedBy: req.userId, updatedAt: new Date() }
          );
          logger.info(`${result.modifiedCount} ${modelName} updated`, { updatedBy: req.userId });
          return res.json({
            message: `${result.modifiedCount} ${modelName} updated successfully`,
            data: result,
            success: true,
            statusCode:200
          });
        }

        case 'bulkDelete': {
          const { ids } = req.body;
          if (!Array.isArray(ids)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid bulk delete payload' }));
          }
          const result = await Model.deleteMany({ _id: { $in: ids } });
          if (result.deletedCount === 0) {
            return next(errorHandler({ statusCode: 404, message: `${modelName} not found` }));
          }
          logger.warn(`${result.deletedCount} ${modelName} deleted`, { deletedBy: req.userId, deletedItem: result});
          return res.json({
            message: `${result.deletedCount} ${modelName} deleted successfully`,
            data: result,
            success: true,
            statusCode:200
          });
        }

        case 'softDelete': {
          if (!mongoose.isValidObjectId(req.params.id)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
          }
          const result = await Model.findByIdAndUpdate(
            req.params.id,
            {
              deleted: true,
              deletedAt: new Date(),
              deletedBy: req.userId
            },
            { new: true }
          );
          logger.warn(`${modelName} soft deleted`, { id: req.params.id, deletedBy: req.userId });
          return res.json({
            message: `${modelName} soft deleted successfully`,
            data: result,
            success: true,
            statusCode:200
          });
        }

        case 'restore': {
          if (!mongoose.isValidObjectId(req.params.id)) {
            return next(errorHandler({ statusCode: 400, message: 'Invalid ID format' }));
          }
          const result = await Model.findByIdAndUpdate(
            req.params.id,
            { $unset: { deleted: "", deletedAt: "", deletedBy: "" } },
            { new: true }
          );
          logger.info(`${modelName} restored`, { id: req.params.id, restoredBy: req.userId });
          return res.json({
            message: `${modelName} restored successfully`,
            data: result,
            success: true,
            statusCode:200
          });
        }

        default:
          return next(errorHandler({ statusCode: 400, message: `Invalid action: ${action}` }));
      }
    } catch (error) {
      if (error.name === 'MongoServerError' && error.code === 11000) {
        const fields = Object.keys(error.keyValue || {}).join(', ');
        return next(errorHandler({
          statusCode: 409,
          message: `Duplicate value for field(s): ${fields}`
        }));
      }
      // Handle Mongoose validation errors
      // if (error.name === 'ValidationError') {
      //   const messages = Object.values(error.errors).map(err => err.message);
      //   return next(errorHandler({
      //     statusCode: 400,
      //     message: messages.join('; ')
      //   }));
      // }
      next(error);
    }
  };
};

const withPermissions = (modelName, action, modulePrefix) => {
  const moduleName = modulePrefix;
  const entityName = modelName.toLowerCase();
  const permissionName = `${moduleName}.${entityName}.${action}`; // e.g., 'hrm.employee.read'

  return [
    permissionChecker(permissionName),
    handler(modelName, action)
  ];
};


export const crudHandler = (modelName, modulePrefix) => {
  const Model = Models[modelName];
  // Check if the model schema has a 'deleted' path for soft delete functionality
  const isSoftDelete = Model?.schema.paths.deleted;

  const handlers = {
    // Standard CRUD operations, now passing the modulePrefix
    list: withPermissions(modelName, 'list', modulePrefix),
    get: withPermissions(modelName, 'get', modulePrefix),
    create: withPermissions(modelName, 'create', modulePrefix),
    update: withPermissions(modelName, 'update', modulePrefix),
    delete: withPermissions(modelName, 'delete', modulePrefix),

    // Bulk operations, passing the modulePrefix
    bulkCreate: withPermissions(modelName, 'bulkCreate', modulePrefix),
    bulkUpdate: withPermissions(modelName, 'bulkUpdate', modulePrefix),
    bulkDelete: withPermissions(modelName, 'bulkDelete', modulePrefix),

    // Advanced filtering - search handler.
    // The `withPermissions` function returns an array of middleware, so we can use its permission check
    // and then add our custom search logic.
    search: [
      withPermissions(modelName, 'search', modulePrefix)[0], // Only the permission check middleware
      async (req, res, next) => {
        try {
          const query = Model.find(req.dataAccessQuery || {}); // Use dataAccessQuery from previous middleware
          const results = await query.exec();
          res.status(200).json({ success: true, statusCode: 200, data: results });
        } catch (error) {
          logger.error(`Error in dynamicController search for ${modelName}:`, { error: error.message, stack: error.stack, userId: req.userId });
          next(errorHandler({ statusCode: 500, message: `Failed to search ${modelName}`, error: error.message }));
        }
      }
    ]
  };

  // Add soft delete endpoints if supported by the model's schema
  if (isSoftDelete) {
    handlers.softDelete = withPermissions(modelName, 'softDelete', modulePrefix);
    handlers.restore = withPermissions(modelName, 'restore', modulePrefix);
    handlers.listDeleted = withPermissions(modelName, 'listDeleted', modulePrefix);
  }

  return handlers;
};
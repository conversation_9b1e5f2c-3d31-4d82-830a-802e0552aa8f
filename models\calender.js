import mongoose from 'mongoose';

// Calendar Schema
const CalendarSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  events: [{
    title: { type: String, required: true },
    description: String,
    type: { 
      type: String, 
      enum: ['meeting', 'interview', 'task', 'reminder', 'event'],
      default: 'event'
    },
    start: { type: Date, required: true },
    end: { type: Date, required: true },
    location: String,
    isVirtual: { type: Boolean, default: false },
    participants: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      email: String,
      status: { type: String, enum: ['pending', 'accepted', 'declined'], default: 'pending' }
    }],
    status: { 
      type: String, 
      enum: ['scheduled', 'completed', 'canceled', 'pending'],
      default: 'scheduled'
    }
  }]
}, { timestamps: true });

export default mongoose.model('Calendar', CalendarSchema);
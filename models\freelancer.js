import mongoose from 'mongoose';

const FreelancerSchema = new mongoose.Schema(
  {
   userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true},
   title: { type: String, trim: true},
   bio: { type: String, trim: true},
   addPhoto: { type: String},
   description: { type: String, trim: true},
   skills: [{name: { type: String, trim: true },level: { type: String, enum: ['beginner', 'intermediate', 'advanced']}}],
   experience: [
      {
        title: { type: String, trim: true },
        company: { type: String, trim: true },
        location: { type: String, trim: true },
        startDate: { type: Date },
        endDate: { type: Date },
        current: { type: Boolean, default: false },
        description: { type: String, trim: true }
      }],
    education: [
      {
        institution: { type: String, trim: true },
        degree: { type: String, trim: true },
        fieldOfStudy: { type: String, trim: true },
        startDate: { type: Date },
        endDate: { type: Date },
        description: { type: String, trim: true },
      }],
    hourlyRate: { type: Number },
    availability: {type: String,trim: true},
    socialLinks: [{
        platform: { 
          type: String, 
          enum: ['linkedin', 'github', 'twitter', 'facebook', 'instagram', 'website', 'telegram', 'other'],
        },
        url: { type: String }
    }],
    certifications: [
      {
        name: { type: String, trim: true },
        issuer: { type: String, trim: true },
        dateObtained: { type: Date },
      }]
  },
  {
    timestamps: true, // adds createdAt and updatedAt fields
  }
);

export default mongoose.model('Freelancer', FreelancerSchema);